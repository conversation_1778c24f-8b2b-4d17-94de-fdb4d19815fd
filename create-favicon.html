<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Favicon for रिया सॉफ्टवेअर</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #ff6b35;
        }
        .favicon-preview {
            width: 64px;
            height: 64px;
            margin: 20px auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        .btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background: #e55a2b;
        }
        canvas {
            display: none;
        }
        .instructions {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Create Favicon for रिया सॉफ्टवेअर</h1>
        
        <div class="favicon-preview" id="preview">
            <span style="font-size: 32px; color: #ff6b35;">र</span>
        </div>
        
        <button class="btn" onclick="generateFavicon()">Generate Favicon.ico</button>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Generate Favicon.ico" to create the favicon</li>
                <li>The favicon.ico file will be downloaded automatically</li>
                <li>Move the downloaded favicon.ico to your project root folder</li>
                <li>The favicon should appear in browser tabs when you open your website</li>
            </ol>
            
            <h3>Multiple Sizes:</h3>
            <p>The generated favicon will include multiple sizes (16x16, 32x32, 48x48) for different uses.</p>
        </div>
    </div>
    
    <canvas id="canvas16"></canvas>
    <canvas id="canvas32"></canvas>
    <canvas id="canvas48"></canvas>
    
    <script>
        function generateFavicon() {
            // Create multiple sizes
            const sizes = [16, 32, 48];
            const canvases = [];
            
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`) || document.createElement('canvas');
                canvas.id = `canvas${size}`;
                canvas.width = size;
                canvas.height = size;
                
                const ctx = canvas.getContext('2d');
                
                // Background circle with gradient
                const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
                gradient.addColorStop(0, '#ff6b35');
                gradient.addColorStop(1, '#f7931e');
                
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/2, 0, 2 * Math.PI);
                ctx.fill();
                
                // Add subtle border
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.lineWidth = 1;
                ctx.stroke();
                
                // Text (Devanagari र)
                ctx.fillStyle = 'white';
                ctx.font = `bold ${Math.floor(size * 0.6)}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('र', size/2, size/2);
                
                canvases.push(canvas);
            });
            
            // For simplicity, we'll download the 32x32 version as PNG
            // In a real scenario, you'd need a library to create proper ICO format
            const canvas32 = canvases[1]; // 32x32 canvas
            
            canvas32.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'favicon-32x32.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                // Also create 16x16 version
                setTimeout(() => {
                    canvases[0].toBlob(function(blob) {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'favicon-16x16.png';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                    });
                }, 500);
                
                // Also create Apple touch icon (180x180)
                setTimeout(() => {
                    createAppleTouchIcon();
                }, 1000);
                
                alert('Favicon files generated! Check your Downloads folder for:\n- favicon-16x16.png\n- favicon-32x32.png\n- apple-touch-icon.png\n\nFor best results, convert the PNG files to ICO format using an online converter.');
            });
        }
        
        function createAppleTouchIcon() {
            const canvas = document.createElement('canvas');
            canvas.width = 180;
            canvas.height = 180;
            const ctx = canvas.getContext('2d');
            
            // Background with rounded corners effect
            const gradient = ctx.createRadialGradient(90, 90, 0, 90, 90, 90);
            gradient.addColorStop(0, '#ff6b35');
            gradient.addColorStop(1, '#f7931e');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 180, 180);
            
            // Add subtle pattern
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            for (let i = 0; i < 180; i += 20) {
                for (let j = 0; j < 180; j += 20) {
                    ctx.fillRect(i, j, 10, 10);
                }
            }
            
            // Main text
            ctx.fillStyle = 'white';
            ctx.font = 'bold 100px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('र', 90, 90);
            
            // Subtitle
            ctx.font = 'bold 20px Arial';
            ctx.fillText('रिया सॉफ्टवेअर', 90, 140);
            
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'apple-touch-icon.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }
    </script>
</body>
</html>
