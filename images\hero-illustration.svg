<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#f7931e;stop-opacity:1" />
        </linearGradient>
        <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
        </linearGradient>
        <linearGradient id="grad3" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
        </linearGradient>
    </defs>
    
    <!-- Background -->
    <rect width="800" height="600" fill="#f8fafc"/>
    
    <!-- Background circles -->
    <circle cx="150" cy="150" r="100" fill="url(#grad1)" opacity="0.1"/>
    <circle cx="650" cy="450" r="120" fill="url(#grad2)" opacity="0.1"/>
    <circle cx="700" cy="100" r="80" fill="url(#grad3)" opacity="0.1"/>
    
    <!-- Main computer/laptop -->
    <rect x="250" y="200" width="300" height="200" rx="15" fill="url(#grad2)"/>
    <rect x="270" y="220" width="260" height="160" rx="5" fill="white"/>
    
    <!-- Screen content -->
    <rect x="290" y="240" width="220" height="15" rx="3" fill="#ff6b35"/>
    <rect x="290" y="270" width="180" height="10" rx="2" fill="#e5e7eb"/>
    <rect x="290" y="290" width="200" height="10" rx="2" fill="#e5e7eb"/>
    <rect x="290" y="310" width="160" height="10" rx="2" fill="#e5e7eb"/>
    
    <!-- Mobile phone -->
    <rect x="580" y="180" width="80" height="140" rx="15" fill="url(#grad1)"/>
    <rect x="590" y="195" width="60" height="110" rx="5" fill="white"/>
    <rect x="600" y="205" width="40" height="8" rx="2" fill="#ff6b35"/>
    <rect x="600" y="220" width="30" height="6" rx="1" fill="#e5e7eb"/>
    <rect x="600" y="235" width="35" height="6" rx="1" fill="#e5e7eb"/>
    <rect x="600" y="250" width="25" height="6" rx="1" fill="#e5e7eb"/>
    
    <!-- Tablet -->
    <rect x="120" y="250" width="100" height="140" rx="10" fill="url(#grad3)"/>
    <rect x="130" y="265" width="80" height="110" rx="5" fill="white"/>
    <rect x="140" y="275" width="60" height="10" rx="2" fill="#10b981"/>
    <rect x="140" y="295" width="50" height="8" rx="1" fill="#e5e7eb"/>
    <rect x="140" y="310" width="55" height="8" rx="1" fill="#e5e7eb"/>
    <rect x="140" y="325" width="45" height="8" rx="1" fill="#e5e7eb"/>
    
    <!-- Floating elements -->
    <circle cx="400" cy="120" r="30" fill="url(#grad1)" opacity="0.8"/>
    <text x="400" y="130" text-anchor="middle" fill="white" font-family="Arial" font-size="20" font-weight="bold">🌐</text>
    
    <circle cx="200" cy="450" r="25" fill="url(#grad2)" opacity="0.8"/>
    <text x="200" y="460" text-anchor="middle" fill="white" font-family="Arial" font-size="18" font-weight="bold">📱</text>
    
    <circle cx="600" cy="380" r="25" fill="url(#grad3)" opacity="0.8"/>
    <text x="600" y="390" text-anchor="middle" fill="white" font-family="Arial" font-size="18" font-weight="bold">⚡</text>
    
    <!-- Code brackets -->
    <text x="100" y="100" fill="url(#grad1)" font-family="monospace" font-size="40" font-weight="bold">&lt;/&gt;</text>
    <text x="650" y="550" fill="url(#grad2)" font-family="monospace" font-size="35" font-weight="bold">{}</text>
    
    <!-- Main title -->
    <text x="400" y="480" text-anchor="middle" fill="url(#grad1)" font-family="Arial" font-size="28" font-weight="bold">Web Development</text>
    <text x="400" y="510" text-anchor="middle" fill="url(#grad2)" font-family="Arial" font-size="20" font-weight="600">रिया सॉफ्टवेअर</text>
    
    <!-- Connecting lines -->
    <line x1="400" y1="150" x2="400" y2="200" stroke="url(#grad1)" stroke-width="3" stroke-dasharray="5,5" opacity="0.6"/>
    <line x1="550" y1="300" x2="580" y2="250" stroke="url(#grad1)" stroke-width="2" stroke-dasharray="3,3" opacity="0.6"/>
    <line x1="250" y1="300" x2="220" y2="320" stroke="url(#grad3)" stroke-width="2" stroke-dasharray="3,3" opacity="0.6"/>
    
    <!-- Decorative dots -->
    <circle cx="50" cy="300" r="4" fill="url(#grad1)" opacity="0.6"/>
    <circle cx="750" cy="200" r="4" fill="url(#grad2)" opacity="0.6"/>
    <circle cx="100" cy="500" r="4" fill="url(#grad3)" opacity="0.6"/>
    <circle cx="700" cy="500" r="4" fill="url(#grad1)" opacity="0.6"/>
</svg>
