<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Placeholder Images for रिया सॉफ्टवेअर</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #ff6b35;
            text-align: center;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .image-placeholder {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #f9f9f9;
        }
        .image-placeholder h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .image-placeholder p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        .download-btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .download-btn:hover {
            background: #e55a2b;
        }
        .instructions {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .instructions h2 {
            color: #1e3a8a;
            margin-top: 0;
        }
        .canvas-container {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>रिया सॉफ्टवेअर - Image Placeholders</h1>
        
        <div class="instructions">
            <h2>Instructions:</h2>
            <p>Click the "Generate" buttons below to create placeholder images for your website. These are temporary placeholders - replace them with actual images for your projects and clients.</p>
            <p><strong>Note:</strong> The hero illustration SVG has already been created. Use the generate-images.html file to create all project and client images automatically.</p>
            <p><strong>Contact Info:</strong> Phone: +91 96040 69989 | Email: <EMAIL></p>
        </div>

        <div class="image-grid">
            <div class="image-placeholder">
                <h3>Hero Illustration</h3>
                <p>Size: 800x600px</p>
                <p>Format: SVG/PNG</p>
                <p>Content: Web development illustration</p>
                <button class="download-btn" onclick="generateImage('hero-illustration', 800, 600, '#ff6b35', 'Web Dev')">Generate SVG</button>
            </div>

            <div class="image-placeholder">
                <h3>Project 1</h3>
                <p>Size: 400x300px</p>
                <p>Content: Local shop website</p>
                <button class="download-btn" onclick="generateImage('project1', 400, 300, '#4f46e5', 'Shop Website')">Generate</button>
            </div>

            <div class="image-placeholder">
                <h3>Project 2</h3>
                <p>Size: 400x300px</p>
                <p>Content: Food ordering app</p>
                <button class="download-btn" onclick="generateImage('project2', 400, 300, '#059669', 'Food App')">Generate</button>
            </div>

            <div class="image-placeholder">
                <h3>Project 3</h3>
                <p>Size: 400x300px</p>
                <p>Content: School management</p>
                <button class="download-btn" onclick="generateImage('project3', 400, 300, '#dc2626', 'School System')">Generate</button>
            </div>

            <div class="image-placeholder">
                <h3>Project 4</h3>
                <p>Size: 400x300px</p>
                <p>Content: E-commerce site</p>
                <button class="download-btn" onclick="generateImage('project4', 400, 300, '#7c3aed', 'E-commerce')">Generate</button>
            </div>

            <div class="image-placeholder">
                <h3>Project 5</h3>
                <p>Size: 400x300px</p>
                <p>Content: Doctor appointment</p>
                <button class="download-btn" onclick="generateImage('project5', 400, 300, '#0891b2', 'Doctor App')">Generate</button>
            </div>

            <div class="image-placeholder">
                <h3>Client 1 - राहुल पाटील</h3>
                <p>Size: 150x150px</p>
                <p>Content: Profile photo</p>
                <button class="download-btn" onclick="generateImage('client1', 150, 150, '#f59e0b', 'RP')">Generate</button>
            </div>

            <div class="image-placeholder">
                <h3>Client 2 - प्रिया शर्मा</h3>
                <p>Size: 150x150px</p>
                <p>Content: Profile photo</p>
                <button class="download-btn" onclick="generateImage('client2', 150, 150, '#ec4899', 'PS')">Generate</button>
            </div>

            <div class="image-placeholder">
                <h3>Client 3 - संजय देशमुख</h3>
                <p>Size: 150x150px</p>
                <p>Content: Profile photo</p>
                <button class="download-btn" onclick="generateImage('client3', 150, 150, '#10b981', 'SD')">Generate</button>
            </div>
        </div>

        <div class="instructions">
            <h2>Free Image Resources:</h2>
            <ul>
                <li><strong>Illustrations:</strong> <a href="https://undraw.co" target="_blank">Undraw.co</a> - Free SVG illustrations</li>
                <li><strong>Photos:</strong> <a href="https://unsplash.com" target="_blank">Unsplash.com</a> - High-quality free photos</li>
                <li><strong>Icons:</strong> <a href="https://fontawesome.com" target="_blank">Font Awesome</a> - Already included in the project</li>
                <li><strong>Stock Photos:</strong> <a href="https://pexels.com" target="_blank">Pexels.com</a> - Free stock photography</li>
            </ul>
        </div>
    </div>

    <div class="canvas-container">
        <canvas id="imageCanvas"></canvas>
    </div>

    <script>
        function generateImage(filename, width, height, color, text) {
            const canvas = document.getElementById('imageCanvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = width;
            canvas.height = height;
            
            // Background
            ctx.fillStyle = color;
            ctx.fillRect(0, 0, width, height);
            
            // Add gradient overlay
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
            gradient.addColorStop(1, 'rgba(0, 0, 0, 0.1)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // Text
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            if (width > 300) {
                ctx.font = 'bold 24px Arial';
            } else {
                ctx.font = 'bold 16px Arial';
            }
            
            ctx.fillText(text, width / 2, height / 2);
            
            // Add size info
            if (width > 200) {
                ctx.font = '12px Arial';
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.fillText(`${width}x${height}`, width / 2, height / 2 + 30);
            }
            
            // Download
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename + '.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }

        // Generate hero SVG
        function generateHeroSVG() {
            const svg = `
                <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#f7931e;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="800" height="600" fill="url(#grad1)"/>
                    <circle cx="400" cy="200" r="80" fill="white" opacity="0.2"/>
                    <rect x="300" y="350" width="200" height="120" rx="10" fill="white" opacity="0.9"/>
                    <rect x="320" y="370" width="160" height="20" rx="5" fill="#ff6b35"/>
                    <rect x="320" y="400" width="120" height="15" rx="3" fill="#ccc"/>
                    <rect x="320" y="425" width="140" height="15" rx="3" fill="#ccc"/>
                    <text x="400" y="300" text-anchor="middle" fill="white" font-family="Arial" font-size="24" font-weight="bold">Web Development</text>
                    <text x="400" y="330" text-anchor="middle" fill="white" font-family="Arial" font-size="16">रिया सॉफ्टवेअर</text>
                </svg>
            `;
            
            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'hero-illustration.svg';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Update the hero illustration button
        document.querySelector('.image-placeholder button').onclick = generateHeroSVG;
    </script>
</body>
</html>
