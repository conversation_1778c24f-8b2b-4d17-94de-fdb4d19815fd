# Deployment Guide - रिया सॉफ्टवेअर Website

This guide will help you deploy your रिया सॉफ्टवेअर website to various hosting platforms.

## Pre-Deployment Checklist

### 1. Content Updates
- [ ] Replace placeholder phone number `+************` with actual number
- [ ] Update email address `<EMAIL>`
- [ ] Add real company address in contact section
- [ ] Replace placeholder images with actual project photos
- [ ] Update client testimonials with real feedback
- [ ] Verify all Marathi content is accurate

### 2. Images Required
- [ ] `hero-illustration.svg` - Hero section illustration
- [ ] `project1.jpg` to `project5.jpg` - Portfolio images (400x300px)
- [ ] `client1.jpg` to `client3.jpg` - Client photos (150x150px)
- [ ] `favicon.ico` - Website favicon
- [ ] `apple-touch-icon.png` - iOS app icon (180x180px)
- [ ] `og-image.jpg` - Social media preview image (1200x630px)

### 3. SEO & Analytics
- [ ] Update meta descriptions
- [ ] Add Google Analytics tracking code
- [ ] Set up Google Search Console
- [ ] Create sitemap.xml
- [ ] Add robots.txt

## Hosting Options

### Option 1: GitHub Pages (Free)
Perfect for static websites with custom domain support.

**Steps:**
1. Create a GitHub repository
2. Upload all files to the repository
3. Go to Settings > Pages
4. Select source branch (main/master)
5. Your site will be available at `https://username.github.io/repository-name`

**Custom Domain:**
1. Add a `CNAME` file with your domain name
2. Configure DNS settings with your domain provider
3. Enable HTTPS in GitHub Pages settings

### Option 2: Netlify (Free Tier Available)
Excellent for static sites with form handling and continuous deployment.

**Steps:**
1. Create account at netlify.com
2. Connect your GitHub repository OR drag & drop files
3. Configure build settings (not needed for static HTML)
4. Your site will get a random URL like `https://amazing-name-123456.netlify.app`

**Custom Domain:**
1. Go to Domain settings in Netlify dashboard
2. Add your custom domain
3. Configure DNS settings as instructed

**Form Handling:**
Netlify can handle your contact form submissions automatically.

### Option 3: Vercel (Free Tier Available)
Great performance and easy deployment.

**Steps:**
1. Create account at vercel.com
2. Import your GitHub repository
3. Deploy with default settings
4. Get URL like `https://project-name.vercel.app`

### Option 4: Traditional Web Hosting
For shared hosting providers like Hostinger, Bluehost, etc.

**Steps:**
1. Purchase hosting plan with cPanel access
2. Upload files via File Manager or FTP
3. Extract files to public_html folder
4. Your site will be live at your domain

## DNS Configuration

### For Custom Domain
If you have a domain like `riyasoftware.com`:

**A Records:**
```
@ → ***************
@ → ***************
@ → ***************
@ → ***************
```

**CNAME Record:**
```
www → username.github.io (for GitHub Pages)
www → amazing-name-123456.netlify.app (for Netlify)
```

## Performance Optimization

### 1. Image Optimization
- Compress images using tools like TinyPNG
- Use WebP format for better compression
- Implement lazy loading (already included)

### 2. Minification
- Minify CSS and JavaScript files
- Use tools like UglifyJS or online minifiers

### 3. CDN Setup
- Use Cloudflare for free CDN
- Configure caching rules
- Enable Brotli compression

## Security Headers

Add these headers to your hosting configuration:

```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://unpkg.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://wa.me;
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
Permissions-Policy: geolocation=(), microphone=(), camera=()
```

## Analytics Setup

### Google Analytics 4
1. Create GA4 property at analytics.google.com
2. Get tracking ID (G-XXXXXXXXXX)
3. Add this code before closing `</head>` tag:

```html
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'G-XXXXXXXXXX');
</script>
```

### Google Search Console
1. Go to search.google.com/search-console
2. Add your domain
3. Verify ownership via DNS or HTML file
4. Submit sitemap.xml

## Contact Form Integration

### Option 1: Netlify Forms (Recommended)
Already configured in the HTML. Just add `netlify` attribute to form:
```html
<form class="contact-form" netlify>
```

### Option 2: Formspree
1. Sign up at formspree.io
2. Replace form action with Formspree endpoint
3. Configure email notifications

### Option 3: EmailJS
1. Sign up at emailjs.com
2. Configure email service
3. Update JavaScript to use EmailJS API

## SSL Certificate

### Free SSL Options:
- **Let's Encrypt**: Automatic with most modern hosting
- **Cloudflare**: Free SSL with their CDN
- **GitHub Pages**: Automatic HTTPS
- **Netlify/Vercel**: Automatic HTTPS

## Backup Strategy

### Regular Backups:
1. **Code**: Keep in Git repository
2. **Images**: Store in cloud storage
3. **Database**: Not applicable for static site
4. **Configuration**: Document all settings

## Monitoring

### Uptime Monitoring:
- UptimeRobot (free)
- Pingdom
- StatusCake

### Performance Monitoring:
- Google PageSpeed Insights
- GTmetrix
- WebPageTest

## Post-Deployment Tasks

### 1. Testing
- [ ] Test all links and buttons
- [ ] Verify contact form submission
- [ ] Check mobile responsiveness
- [ ] Test WhatsApp integration
- [ ] Validate HTML/CSS

### 2. SEO Setup
- [ ] Submit to Google Search Console
- [ ] Create Google My Business listing
- [ ] Submit to local directories
- [ ] Set up social media profiles

### 3. Marketing
- [ ] Share on social media
- [ ] Add to business cards
- [ ] Update email signatures
- [ ] Inform existing clients

## Troubleshooting

### Common Issues:

**Images not loading:**
- Check file paths and case sensitivity
- Verify images are uploaded to correct folder
- Check file permissions

**Contact form not working:**
- Verify form action URL
- Check spam folder for submissions
- Test with different email addresses

**Mobile layout issues:**
- Test on actual devices
- Use browser developer tools
- Check viewport meta tag

**Slow loading:**
- Optimize images
- Enable compression
- Use CDN
- Minify CSS/JS

## Support

For technical support:
- Check browser console for errors
- Validate HTML at validator.w3.org
- Test CSS at jigsaw.w3.org/css-validator
- Use Google PageSpeed Insights for performance

---

**Remember:** Always test your website thoroughly before announcing it to clients and customers!

Good luck with your रिया सॉफ्टवेअर website! 🚀
