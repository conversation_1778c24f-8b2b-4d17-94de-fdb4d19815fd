# रिया सॉफ्टवेअर - Website

A modern, responsive website for रिया सॉफ्टवेअर (Riya Software) - a Website Development and Software Development company based in Maharashtra, India.

## Features

### 🌟 Modern Design
- Clean, professional layout with Marathi typography
- Responsive design that works on all devices
- Smooth animations and transitions
- Cultural color palette (saffron, white, deep blue, green)

### 📱 Mobile-First Approach
- Fully responsive design
- Touch-friendly navigation
- Optimized for mobile performance
- Progressive Web App ready

### 🎯 Key Sections
1. **Hero Section** - Welcoming headline with call-to-action buttons
2. **Services** - Complete list of digital services offered
3. **Portfolio** - Showcase of past projects
4. **Testimonials** - Client feedback and reviews
5. **FAQ** - Common questions answered in Marathi
6. **Contact** - Contact form with WhatsApp integration

### 🚀 Interactive Features
- Fixed quick action buttons (Call, WhatsApp, Website, App)
- Floating WhatsApp chat widget
- Smooth scroll animations
- FAQ accordion
- Contact form with WhatsApp integration
- Mobile hamburger menu

### 🎨 Typography & Fonts
- **Primary**: <PERSON><PERSON> (for Marathi text)
- **Secondary**: Hind (fallback for Marathi)
- Optimized for readability in Marathi script

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # CSS styles and animations
├── script.js           # JavaScript functionality
├── images/             # Image assets directory
└── README.md           # This file
```

## Setup Instructions

1. **Clone or Download** the project files
2. **Add Images**: Place the following images in the `images/` folder:
   - `hero-illustration.svg` - Hero section illustration
   - `project1.jpg` to `project5.jpg` - Portfolio project images
   - `client1.jpg` to `client3.jpg` - Client testimonial photos

3. **Update Contact Information**:
   - Replace `+919876543210` with your actual phone number
   - Update `<EMAIL>` with your email
   - Modify the address in the contact section

4. **Customize Content**:
   - Update company information in the footer
   - Modify service descriptions as needed
   - Add your actual project portfolio
   - Update client testimonials

## Image Requirements

### Hero Section
- `hero-illustration.svg` - Modern illustration representing web/app development
- Recommended size: 800x600px
- Format: SVG preferred, PNG acceptable

### Portfolio Images
- `project1.jpg` - Local shop website
- `project2.jpg` - Food ordering app
- `project3.jpg` - School management system
- `project4.jpg` - E-commerce website
- `project5.jpg` - Doctor appointment app
- Recommended size: 400x300px
- Format: JPG or PNG

### Client Photos
- `client1.jpg` - राहुल पाटील
- `client2.jpg` - प्रिया शर्मा
- `client3.jpg` - संजय देशमुख
- Recommended size: 150x150px (square)
- Format: JPG or PNG

## Customization Guide

### Colors
The website uses CSS custom properties for easy color customization:

```css
:root {
    --primary-color: #ff6b35;    /* Main orange */
    --secondary-color: #f7931e;  /* Secondary orange */
    --accent-color: #1e3a8a;     /* Deep blue */
    --success-color: #10b981;    /* Green */
}
```

### Contact Information
Update these in multiple places:
- Phone numbers in HTML
- WhatsApp links
- Email addresses
- Physical address

### Services
Modify the services section in `index.html` to match your offerings:
- Service titles
- Service descriptions
- Service icons (Font Awesome classes)

## Browser Support

- ✅ Chrome 60+
- ✅ Firefox 60+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Features

- Optimized images with lazy loading
- Minified CSS and JavaScript
- Efficient animations using CSS transforms
- Mobile-first responsive design
- Fast loading times

## SEO Features

- Semantic HTML structure
- Meta tags for search engines
- Open Graph tags for social sharing
- Structured data markup ready
- Fast loading speeds

## Accessibility

- ARIA labels for screen readers
- Keyboard navigation support
- High contrast color ratios
- Semantic HTML elements
- Alt text for images

## Technologies Used

- **HTML5** - Semantic markup
- **CSS3** - Modern styling with Grid and Flexbox
- **JavaScript (ES6+)** - Interactive functionality
- **AOS Library** - Scroll animations
- **Font Awesome** - Icons
- **Google Fonts** - Marathi typography

## WhatsApp Integration

The contact form automatically creates a WhatsApp message with:
- Customer name and contact details
- Selected service
- Custom message
- Professional formatting in Marathi

## Future Enhancements

- [ ] Add Google Analytics tracking
- [ ] Implement service worker for offline functionality
- [ ] Add blog section
- [ ] Integrate with CMS
- [ ] Add online payment gateway
- [ ] Multi-language support (Hindi, English)

## Support

For technical support or customization requests:
- 📞 Phone: +91 98765 43210
- 💬 WhatsApp: +91 98765 43210
- 📧 Email: <EMAIL>

## License

© 2025 रिया सॉफ्टवेअर. All rights reserved.

---

**Made with ❤️ for Maharashtra's digital transformation!**
