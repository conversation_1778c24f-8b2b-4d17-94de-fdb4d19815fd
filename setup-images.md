# Image Setup Guide for रिया सॉफ्टवेअर Website

## Quick Setup

### Step 1: Generate Images
1. Open `generate-images.html` in your browser
2. Click "Generate All Images" button
3. All images will be automatically downloaded to your Downloads folder

### Step 2: Move Images to Project
Move the downloaded images from your Downloads folder to the `images/` folder:

**Project Images (400x300px):**
- `project1.jpg` → `images/project1.jpg`
- `project2.jpg` → `images/project2.jpg`
- `project3.jpg` → `images/project3.jpg`
- `project4.jpg` → `images/project4.jpg`
- `project5.jpg` → `images/project5.jpg`

**Client Images (150x150px):**
- `client1.jpg` → `images/client1.jpg`
- `client2.jpg` → `images/client2.jpg`
- `client3.jpg` → `images/client3.jpg`

### Step 3: Create Favicon
1. Use an online converter to convert `images/favicon.svg` to `favicon.ico`
2. Recommended: https://convertio.co/svg-ico/
3. Place the `favicon.ico` file in the root directory

### Step 4: Optional - Replace with Real Images
For better results, replace the generated placeholder images with:

**Project Images:**
- Screenshots of actual websites/apps you've built
- Mockups of your work
- Professional project photos

**Client Images:**
- Real client photos (with permission)
- Professional headshots
- Company logos (if no personal photos available)

## Image Specifications

### Hero Illustration
- ✅ **Already created:** `images/hero-illustration.svg`
- Size: 800x600px
- Format: SVG (scalable)
- Content: Web development themed illustration

### Project Portfolio Images
- Size: 400x300px
- Format: JPG (recommended for photos)
- Content: Screenshots or mockups of your projects

### Client Testimonial Images
- Size: 150x150px (square)
- Format: JPG
- Content: Client photos or initials

### Favicon
- Size: 32x32px (and multiple sizes for different devices)
- Format: ICO
- Content: Company logo or "र" symbol

## Free Image Resources

### For Project Images:
- **Unsplash.com** - High-quality free photos
- **Pexels.com** - Free stock photography
- **Pixabay.com** - Free images and vectors

### For Illustrations:
- **Undraw.co** - Free SVG illustrations
- **Storyset.com** - Animated illustrations
- **Freepik.com** - Vector graphics (attribution required)

### For Icons:
- **Font Awesome** - Already included in the project
- **Heroicons.com** - Beautiful hand-crafted SVG icons
- **Feathericons.com** - Simply beautiful open source icons

## Image Optimization Tips

### Before Upload:
1. **Compress images** using TinyPNG.com or similar
2. **Resize to exact dimensions** needed
3. **Use WebP format** for better compression (optional)
4. **Add alt text** for accessibility

### File Naming:
- Use descriptive names: `ecommerce-website-project.jpg`
- Avoid spaces: use hyphens or underscores
- Keep names short but meaningful

## Current Image Status

### ✅ Completed:
- `images/hero-illustration.svg` - Hero section illustration
- `images/favicon.svg` - Favicon source file

### 🔄 To Generate:
- `project1.jpg` to `project5.jpg` - Portfolio images
- `client1.jpg` to `client3.jpg` - Client photos
- `favicon.ico` - Website favicon

### 📝 Optional Additions:
- `og-image.jpg` (1200x630px) - Social media preview
- `apple-touch-icon.png` (180x180px) - iOS app icon
- `icon-192x192.png` - PWA icon
- `icon-512x512.png` - PWA icon

## Testing Images

After adding images, test:
1. **Load website** and check all images display correctly
2. **Test on mobile** to ensure responsive behavior
3. **Check loading speed** - optimize if needed
4. **Validate alt text** for accessibility

## Troubleshooting

### Images not showing:
- Check file paths are correct
- Ensure images are in the `images/` folder
- Verify file names match exactly (case-sensitive)
- Check file permissions

### Slow loading:
- Compress images using TinyPNG
- Consider using WebP format
- Implement lazy loading (already included)

### Poor quality:
- Use higher resolution source images
- Avoid over-compression
- Use appropriate file formats (JPG for photos, PNG for graphics)

---

**Need Help?** Contact रिया सॉफ्टवेअर:
- 📞 Phone: +91 96040 69989
- 💬 WhatsApp: +91 96040 69989
- 📧 Email: <EMAIL>
