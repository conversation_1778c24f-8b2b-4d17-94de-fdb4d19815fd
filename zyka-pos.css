/* Zyka POS Specific Styles */

/* POS Hero Section */
.pos-hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #06b6d4 100%);
    color: var(--white);
    padding-top: 80px;
    position: relative;
    overflow: hidden;
}

.pos-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.pos-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.pos-logo h1 {
    font-size: 4rem;
    font-weight: 800;
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.pos-logo .tagline {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.pos-hero .hero-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.pos-hero .hero-content p {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    line-height: 1.6;
}

/* POS Mockup */
.pos-mockup {
    position: relative;
    max-width: 400px;
    margin: 0 auto;
}

.screen {
    background: #1f2937;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 3px solid #374151;
}

.pos-interface {
    background: #111827;
    border-radius: 12px;
    padding: 15px;
    color: white;
    font-family: 'Courier New', monospace;
}

.header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #374151;
    margin-bottom: 15px;
}

.logo-small {
    color: #fbbf24;
    font-weight: bold;
    font-size: 14px;
}

.time {
    color: #9ca3af;
    font-size: 12px;
}

.menu-items {
    margin-bottom: 15px;
}

.item {
    padding: 8px 0;
    border-bottom: 1px solid #374151;
    font-size: 14px;
    color: #e5e7eb;
}

.total {
    text-align: right;
    font-weight: bold;
    color: #10b981;
    font-size: 16px;
    padding-top: 10px;
    border-top: 2px solid #374151;
}

/* Features Section */
.pos-features {
    padding: 6rem 0;
    background: var(--light-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
    gap: 2.5rem;
}

.feature-card {
    background: var(--white);
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1e3a8a, #3b82f6, #06b6d4);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: #3b82f6;
}

.feature-icon {
    width: 90px;
    height: 90px;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2.2rem;
    color: var(--white);
    position: relative;
}

.feature-icon::after {
    content: '';
    position: absolute;
    inset: -3px;
    background: linear-gradient(45deg, #1e3a8a, #3b82f6, #06b6d4);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card:hover .feature-icon::after {
    opacity: 1;
}

.feature-card h3 {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.feature-card p {
    color: var(--text-light);
    line-height: 1.7;
    font-size: 1.1rem;
}

/* Pricing Section */
.pos-pricing {
    padding: 6rem 0;
    background: var(--white);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(320px, 100%), 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2.5rem 2rem;
    text-align: center;
    transition: var(--transition);
    border: 2px solid transparent;
    position: relative;
}

.pricing-card.featured {
    border-color: #3b82f6;
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.plan-header h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.price {
    margin-bottom: 2rem;
}

.currency {
    font-size: 1.5rem;
    color: var(--text-light);
    vertical-align: top;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: #3b82f6;
}

.period {
    font-size: 1rem;
    color: var(--text-light);
    margin-left: 0.5rem;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
}

.plan-features li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.plan-features li:last-child {
    border-bottom: none;
}

.plan-features i {
    color: #10b981;
    font-size: 1.1rem;
}

.btn-outline {
    background: transparent;
    color: #3b82f6;
    border: 2px solid #3b82f6;
}

.btn-outline:hover {
    background: #3b82f6;
    color: white;
}

/* Demo Section */
.pos-demo {
    padding: 6rem 0;
    background: var(--light-bg);
}

.demo-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.demo-video {
    position: relative;
}

.video-placeholder {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    border-radius: var(--border-radius);
    padding: 4rem 2rem;
    text-align: center;
    color: white;
    cursor: pointer;
    transition: var(--transition);
}

.video-placeholder:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

.video-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.video-placeholder p {
    font-size: 1.3rem;
    font-weight: 600;
}

.demo-info h3 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.demo-info p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.demo-benefits {
    list-style: none;
    padding: 0;
    margin-bottom: 2.5rem;
}

.demo-benefits li {
    padding: 0.75rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-dark);
}

.demo-benefits i {
    color: #10b981;
    font-size: 1.1rem;
}

/* CTA Section */
.pos-cta {
    padding: 6rem 0;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.cta-content p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-large {
    padding: 1.2rem 2.5rem;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Active Navigation */
.nav-menu a.active {
    color: var(--primary-color);
    font-weight: 600;
}

.nav-menu a.active::after {
    width: 100%;
}

/* Responsive Design for POS Page */
@media (max-width: 768px) {
    .pos-logo h1 {
        font-size: 2.5rem;
    }
    
    .pos-hero .hero-content h2 {
        font-size: 1.8rem;
    }
    
    .pos-hero .hero-content p {
        font-size: 1.1rem;
    }
    
    .demo-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-large {
        width: 100%;
        max-width: 300px;
    }
    
    .pricing-card.featured {
        transform: none;
    }
    
    .features-grid {
        gap: 1.5rem;
    }
    
    .feature-card {
        padding: 2rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .pos-logo h1 {
        font-size: 2rem;
    }
    
    .pos-logo .tagline {
        font-size: 1rem;
    }
    
    .pos-hero .hero-content h2 {
        font-size: 1.5rem;
    }
    
    .cta-content h2 {
        font-size: 1.8rem;
    }
    
    .feature-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }
    
    .pricing-card {
        padding: 2rem 1.5rem;
    }
    
    .amount {
        font-size: 2.5rem;
    }
}
