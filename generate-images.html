<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Images for रिया सॉफ्टवेअर</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
        }
        h1 {
            color: #ff6b35;
            text-align: center;
        }
        .btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .btn:hover {
            background: #e55a2b;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .image-card {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Generate Images for रिया सॉफ्टवेअर Website</h1>
        <p>Click the buttons below to generate and download placeholder images for your website.</p>
        
        <div class="grid">
            <div class="image-card">
                <h3>Project 1</h3>
                <p>Local Shop Website</p>
                <button class="btn" onclick="generateProjectImage('project1', 'Shop Website', '#4f46e5')">Generate</button>
            </div>
            
            <div class="image-card">
                <h3>Project 2</h3>
                <p>Food Ordering App</p>
                <button class="btn" onclick="generateProjectImage('project2', 'Food App', '#059669')">Generate</button>
            </div>
            
            <div class="image-card">
                <h3>Project 3</h3>
                <p>School Management</p>
                <button class="btn" onclick="generateProjectImage('project3', 'School System', '#dc2626')">Generate</button>
            </div>
            
            <div class="image-card">
                <h3>Project 4</h3>
                <p>E-commerce Website</p>
                <button class="btn" onclick="generateProjectImage('project4', 'E-commerce', '#7c3aed')">Generate</button>
            </div>
            
            <div class="image-card">
                <h3>Project 5</h3>
                <p>Doctor Appointment</p>
                <button class="btn" onclick="generateProjectImage('project5', 'Doctor App', '#0891b2')">Generate</button>
            </div>
            
            <div class="image-card">
                <h3>Client 1</h3>
                <p>राहुल पाटील</p>
                <button class="btn" onclick="generateClientImage('client1', 'RP', '#f59e0b')">Generate</button>
            </div>
            
            <div class="image-card">
                <h3>Client 2</h3>
                <p>प्रिया शर्मा</p>
                <button class="btn" onclick="generateClientImage('client2', 'PS', '#ec4899')">Generate</button>
            </div>
            
            <div class="image-card">
                <h3>Client 3</h3>
                <p>संजय देशमुख</p>
                <button class="btn" onclick="generateClientImage('client3', 'SD', '#10b981')">Generate</button>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="generateAllImages()" style="font-size: 18px; padding: 15px 30px;">Generate All Images</button>
        </div>
    </div>
    
    <canvas id="canvas"></canvas>
    
    <script>
        function generateProjectImage(filename, title, color) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = 400;
            canvas.height = 300;
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, 400, 300);
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, adjustBrightness(color, -20));
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 300);
            
            // Overlay pattern
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            for (let i = 0; i < 400; i += 40) {
                for (let j = 0; j < 300; j += 40) {
                    ctx.fillRect(i, j, 20, 20);
                }
            }
            
            // Main content area
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(50, 80, 300, 140);
            
            // Title
            ctx.fillStyle = color;
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(title, 200, 130);
            
            // Subtitle
            ctx.fillStyle = '#666';
            ctx.font = '16px Arial';
            ctx.fillText('रिया सॉफ्टवेअर', 200, 160);
            
            // Size info
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.font = '12px Arial';
            ctx.fillText('400x300', 200, 280);
            
            // Download
            downloadCanvas(canvas, filename + '.jpg');
        }
        
        function generateClientImage(filename, initials, color) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = 150;
            canvas.height = 150;
            
            // Background circle
            const gradient = ctx.createRadialGradient(75, 75, 0, 75, 75, 75);
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, adjustBrightness(color, -30));
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(75, 75, 75, 0, 2 * Math.PI);
            ctx.fill();
            
            // Inner circle
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.beginPath();
            ctx.arc(75, 75, 60, 0, 2 * Math.PI);
            ctx.fill();
            
            // Initials
            ctx.fillStyle = 'white';
            ctx.font = 'bold 36px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(initials, 75, 75);
            
            // Download
            downloadCanvas(canvas, filename + '.jpg');
        }
        
        function adjustBrightness(hex, percent) {
            const num = parseInt(hex.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }
        
        function downloadCanvas(canvas, filename) {
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/jpeg', 0.9);
        }
        
        function generateAllImages() {
            const projects = [
                {name: 'project1', title: 'Shop Website', color: '#4f46e5'},
                {name: 'project2', title: 'Food App', color: '#059669'},
                {name: 'project3', title: 'School System', color: '#dc2626'},
                {name: 'project4', title: 'E-commerce', color: '#7c3aed'},
                {name: 'project5', title: 'Doctor App', color: '#0891b2'}
            ];
            
            const clients = [
                {name: 'client1', initials: 'RP', color: '#f59e0b'},
                {name: 'client2', initials: 'PS', color: '#ec4899'},
                {name: 'client3', initials: 'SD', color: '#10b981'}
            ];
            
            let delay = 0;
            
            projects.forEach(project => {
                setTimeout(() => {
                    generateProjectImage(project.name, project.title, project.color);
                }, delay);
                delay += 500;
            });
            
            clients.forEach(client => {
                setTimeout(() => {
                    generateClientImage(client.name, client.initials, client.color);
                }, delay);
                delay += 500;
            });
            
            alert('All images will be generated and downloaded automatically!');
        }
    </script>
</body>
</html>
